"""
AI Gen Hub 请求模型定义

包含所有类型的请求模型：
- 传统格式的请求模型（保持向后兼容）
- 优化版本的请求模型（新的结构化设计）
- 各种配置模型（生成配置、流式配置、安全配置等）

重构说明：
- 整合了原有 interfaces.py 和 docs/api_optimization/ 中的请求模型
- 保持向后兼容性，支持传统格式和优化版本
- 提供渐进式迁移支持和供应商兼容性
"""

import logging
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator

from .base import (
    BaseRequest,
    BaseConfig,
    Message,
    MessageRole,
    ModelType,
    generate_id,
    validate_enum_value,
)

# 设置日志记录器
logger = logging.getLogger(__name__)


# =============================================================================
# 配置模型
# =============================================================================

class GenerationConfig(BaseConfig):
    """生成配置参数组
    
    将文本生成相关的参数组织到一个配置类中，提供更好的结构化和验证。
    这个设计使得参数管理更加清晰，同时便于不同供应商的参数映射。
    """
    max_tokens: Optional[int] = Field(
        None,
        ge=1,
        le=32768,
        description="最大生成token数，不设置则使用模型默认值"
    )
    temperature: float = Field(
        0.7,
        ge=0.0,
        le=2.0,
        description="生成温度，控制随机性。0.0为确定性输出，2.0为最大随机性"
    )
    top_p: Optional[float] = Field(
        None,
        ge=0.0,
        le=1.0,
        description="核采样参数，与temperature互斥使用"
    )
    top_k: Optional[int] = Field(
        None,
        ge=1,
        le=100,
        description="Top-K采样参数，限制候选token数量"
    )
    frequency_penalty: float = Field(
        0.0,
        ge=-2.0,
        le=2.0,
        description="频率惩罚，减少重复内容"
    )
    presence_penalty: float = Field(
        0.0,
        ge=-2.0,
        le=2.0,
        description="存在惩罚，鼓励话题多样性"
    )
    stop: Optional[Union[str, List[str]]] = Field(
        None,
        description="停止序列，遇到时停止生成"
    )

    @validator('stop')
    def validate_stop_sequences(cls, v):
        """验证停止序列"""
        if v is None:
            return v
        if isinstance(v, str):
            return [v] if v.strip() else None
        if isinstance(v, list):
            # 过滤空字符串并限制数量
            filtered = [s for s in v if isinstance(s, str) and s.strip()]
            if len(filtered) > 10:
                raise ValueError("停止序列数量不能超过10个")
            return filtered if filtered else None
        raise ValueError("停止序列必须是字符串或字符串列表")


class StreamConfig(BaseConfig):
    """流式输出配置"""
    enabled: bool = Field(False, description="是否启用流式输出")
    chunk_size: Optional[int] = Field(
        None,
        ge=0,
        le=1000,
        description="流式输出块大小（字符数），0表示使用默认块大小"
    )
    include_usage: bool = Field(
        True,
        description="是否在流式输出中包含使用量统计"
    )


class SafetyConfig(BaseConfig):
    """安全配置参数"""
    content_filter: bool = Field(True, description="是否启用内容过滤")
    safety_level: str = Field(
        "medium",
        pattern="^(low|medium|high|strict)$",
        description="安全级别：low, medium, high, strict"
    )
    custom_filters: Optional[List[str]] = Field(
        None,
        description="自定义过滤规则"
    )


# =============================================================================
# 传统格式请求模型（保持向后兼容）
# =============================================================================

class TextGenerationRequest(BaseRequest):
    """传统格式的文本生成请求模型
    
    保持与现有API的完全兼容性，支持扁平化的参数结构。
    """
    messages: List[Message] = Field(..., description="对话消息列表")
    model: str = Field(..., description="使用的模型名称")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0, description="核采样参数")
    top_k: Optional[int] = Field(None, ge=1, description="Top-K采样参数")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="存在惩罚")
    stop: Optional[Union[str, List[str]]] = Field(None, description="停止序列")
    stream: bool = Field(False, description="是否流式输出")
    functions: Optional[List[Dict[str, Any]]] = Field(None, description="可用函数列表")
    function_call: Optional[Union[str, Dict[str, str]]] = Field(None, description="函数调用设置")
    tools: Optional[List[Dict[str, Any]]] = Field(None, description="可用工具列表")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(None, description="工具选择设置")

    # 结构化输出相关参数
    response_format: Optional[Dict[str, Any]] = Field(None, description="响应格式配置")
    response_schema: Optional[Dict[str, Any]] = Field(None, description="JSON Schema 约束")

    # Thinking 配置参数（Gemini 2.5 专用）
    thinking_budget: Optional[int] = Field(None, description="思考过程的 token 预算")
    thinking_config: Optional[Dict[str, Any]] = Field(None, description="思考配置")

    # 安全设置参数
    safety_settings: Optional[List[Dict[str, Any]]] = Field(None, description="安全设置配置")

    # 供应商特定参数
    provider_params: Dict[str, Any] = Field(default_factory=dict, description="供应商特定参数")


class ImageGenerationRequest(BaseRequest):
    """图像生成请求模型"""
    prompt: str = Field(..., description="图像描述提示")
    model: Optional[str] = Field(None, description="使用的模型名称")
    n: int = Field(1, ge=1, le=10, description="生成图像数量")
    size: str = Field("1024x1024", description="图像尺寸")
    quality: str = Field("standard", description="图像质量")
    style: Optional[str] = Field(None, description="图像风格")
    response_format: str = Field("url", description="响应格式")

    # Gemini 图像生成特定参数
    input_images: Optional[List[str]] = Field(None, description="输入图像列表（用于图像编辑）")
    response_modalities: Optional[List[str]] = Field(None, description="响应模态（TEXT, IMAGE）")
    edit_instruction: Optional[str] = Field(None, description="图像编辑指令")

    # 供应商特定参数
    provider_params: Dict[str, Any] = Field(default_factory=dict, description="供应商特定参数")


# =============================================================================
# 优化版本请求模型
# =============================================================================

class OptimizedTextGenerationRequest(BaseRequest):
    """优化版本的文本生成请求模型

    这是新的优化版本请求格式，提供了以下改进：
    1. 参数分组：将相关参数组织到子配置中，提高可读性和可维护性
    2. 更好的默认值：基于最佳实践设置合理默认值
    3. 增强验证：添加更严格的参数验证和智能建议
    4. 供应商兼容性：内置供应商适配和兼容性检查功能
    5. 向后兼容：支持与传统格式的双向转换

    使用示例：
        >>> request = OptimizedTextGenerationRequest(
        ...     messages=[Message(role=MessageRole.USER, content="Hello")],
        ...     model="gpt-4",
        ...     generation=GenerationConfig(temperature=0.8, max_tokens=1000),
        ...     stream=StreamConfig(enabled=True)
        ... )
    """

    # === 核心必需参数 ===
    messages: List[Message] = Field(
        ...,
        min_items=1,
        description="对话消息列表，至少包含一条消息"
    )
    model: str = Field(
        ...,
        min_length=1,
        description="使用的模型名称，如 'gpt-4', 'claude-3-sonnet'"
    )

    # === 生成配置 ===
    generation: GenerationConfig = Field(
        default_factory=GenerationConfig,
        description="文本生成配置参数"
    )

    # === 流式配置 ===
    stream: StreamConfig = Field(
        default_factory=StreamConfig,
        description="流式输出配置"
    )

    # === 安全配置 ===
    safety: Optional[SafetyConfig] = Field(
        None,
        description="安全配置，不设置则使用默认安全策略"
    )

    # === 高级功能参数 ===
    functions: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="可用函数列表，用于函数调用功能"
    )
    function_call: Optional[Union[str, Dict[str, str]]] = Field(
        None,
        description="函数调用设置：'auto', 'none', 或指定函数"
    )
    tools: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="可用工具列表"
    )
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(
        None,
        description="工具选择设置"
    )

    # === 结构化输出参数 ===
    response_format: Optional[Dict[str, Any]] = Field(
        None,
        description="响应格式配置，支持JSON Schema约束"
    )
    response_schema: Optional[Dict[str, Any]] = Field(
        None,
        description="JSON Schema约束"
    )

    # === 供应商特定参数 ===
    provider_params: Dict[str, Any] = Field(
        default_factory=dict,
        description="供应商特定参数，透传给底层API"
    )

    @validator('messages')
    def validate_messages(cls, v):
        """验证消息列表"""
        if not v:
            raise ValueError("消息列表不能为空")

        # 检查消息角色顺序的合理性
        roles = [msg.role for msg in v]

        # 第一条消息通常应该是system或user
        if roles[0] not in [MessageRole.SYSTEM, MessageRole.USER]:
            logger.warning("第一条消息建议使用system或user角色")

        return v

    @validator('model')
    def validate_model(cls, v):
        """验证模型名称"""
        if not v or not v.strip():
            raise ValueError("模型名称不能为空")
        return v.strip()

    @validator('generation')
    def validate_generation_config(cls, v):
        """验证生成配置"""
        # 检查temperature和top_p是否同时设置
        if v.temperature != 0.7 and v.top_p is not None:
            logger.warning("建议只使用temperature或top_p其中一个参数")
        return v

    # ========================================================================
    # 渐进式迁移支持方法
    # ========================================================================

    @classmethod
    def from_legacy_request(cls, legacy_request: Union[Dict[str, Any], "TextGenerationRequest"]) -> "OptimizedTextGenerationRequest":
        """从传统的TextGenerationRequest格式转换为优化版本

        这个方法实现了向后兼容性，允许现有代码逐步迁移到优化版本。
        它会智能地将扁平化的参数重新组织到相应的配置组中。

        Args:
            legacy_request: 传统格式的请求参数字典或TextGenerationRequest对象

        Returns:
            OptimizedTextGenerationRequest: 转换后的优化版本请求

        Example:
            >>> legacy_data = {
            ...     "messages": [{"role": "user", "content": "Hello"}],
            ...     "model": "gpt-4",
            ...     "temperature": 0.8,
            ...     "max_tokens": 1000,
            ...     "stream": True
            ... }
            >>> optimized_request = OptimizedTextGenerationRequest.from_legacy_request(legacy_data)
        """
        logger.info("正在将传统请求格式转换为优化版本")

        # 如果是TextGenerationRequest对象，先转换为字典
        if hasattr(legacy_request, 'dict'):
            legacy_dict = legacy_request.dict()
        else:
            legacy_dict = legacy_request

        # 提取核心必需参数
        messages = legacy_dict.get("messages", [])
        model = legacy_dict.get("model", "")

        # 构建生成配置
        generation_params = {}
        for param in ["max_tokens", "temperature", "top_p", "top_k",
                     "frequency_penalty", "presence_penalty", "stop"]:
            if param in legacy_dict:
                generation_params[param] = legacy_dict[param]

        generation_config = GenerationConfig(**generation_params)

        # 构建流式配置
        stream_enabled = legacy_dict.get("stream", False)
        stream_config = StreamConfig(enabled=stream_enabled)

        # 构建安全配置（如果有安全相关参数）
        safety_config = None
        if "safety_settings" in legacy_dict:
            safety_config = SafetyConfig(
                content_filter=True,
                safety_level="medium"  # 默认安全级别
            )

        # 提取高级功能参数
        functions = legacy_dict.get("functions")
        function_call = legacy_dict.get("function_call")
        tools = legacy_dict.get("tools")
        tool_choice = legacy_dict.get("tool_choice")
        response_format = legacy_dict.get("response_format")
        response_schema = legacy_dict.get("response_schema")
        user = legacy_dict.get("user")

        # 提取供应商特定参数
        provider_params = legacy_dict.get("provider_params", {})

        # 添加thinking相关参数到provider_params（如果存在）
        if "thinking_budget" in legacy_dict:
            provider_params["thinking_budget"] = legacy_dict["thinking_budget"]
        if "thinking_config" in legacy_dict:
            provider_params["thinking_config"] = legacy_dict["thinking_config"]

        # 构建优化版本请求
        return cls(
            messages=messages,
            model=model,
            generation=generation_config,
            stream=stream_config,
            safety=safety_config,
            functions=functions,
            function_call=function_call,
            tools=tools,
            tool_choice=tool_choice,
            response_format=response_format,
            response_schema=response_schema,
            user=user,
            provider_params=provider_params
        )

    def to_legacy_format(self) -> Dict[str, Any]:
        """转换为传统的扁平化格式

        这个方法确保优化版本可以与现有的供应商适配器兼容，
        在迁移期间提供双向转换能力。

        Returns:
            Dict[str, Any]: 传统格式的请求参数
        """
        logger.info("正在将优化版本转换为传统格式")

        # 基础参数
        legacy_format = {
            "messages": self.messages,
            "model": self.model,
        }

        # 展开生成配置
        if self.generation.max_tokens is not None:
            legacy_format["max_tokens"] = self.generation.max_tokens
        legacy_format["temperature"] = self.generation.temperature
        if self.generation.top_p is not None:
            legacy_format["top_p"] = self.generation.top_p
        if self.generation.top_k is not None:
            legacy_format["top_k"] = self.generation.top_k
        legacy_format["frequency_penalty"] = self.generation.frequency_penalty
        legacy_format["presence_penalty"] = self.generation.presence_penalty
        if self.generation.stop is not None:
            legacy_format["stop"] = self.generation.stop

        # 流式配置
        legacy_format["stream"] = self.stream.enabled

        # 高级功能参数
        if self.functions is not None:
            legacy_format["functions"] = self.functions
        if self.function_call is not None:
            legacy_format["function_call"] = self.function_call
        if self.tools is not None:
            legacy_format["tools"] = self.tools
        if self.tool_choice is not None:
            legacy_format["tool_choice"] = self.tool_choice
        if self.response_format is not None:
            legacy_format["response_format"] = self.response_format
        if self.response_schema is not None:
            legacy_format["response_schema"] = self.response_schema
        if self.user is not None:
            legacy_format["user"] = self.user

        # 安全设置
        if self.safety is not None:
            legacy_format["safety_settings"] = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": self.safety.safety_level.upper()
                }
            ]

        # 供应商特定参数
        legacy_format["provider_params"] = self.provider_params.copy()

        return legacy_format


# =============================================================================
# 简化版本请求模型（向后兼容）
# =============================================================================

class SimpleTextGenerationRequest(BaseRequest):
    """简化版本的文本生成请求，保持向后兼容性

    为那些不需要复杂配置的用户提供简单的接口，
    同时支持转换为优化版本以获得完整功能。
    """

    messages: List[Message] = Field(..., description="对话消息列表")
    model: str = Field(..., description="使用的模型名称")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="生成温度")
    stream: bool = Field(False, description="是否流式输出")

    def to_optimized(self) -> OptimizedTextGenerationRequest:
        """转换为优化版本的请求

        这个方法提供了从简化版本到优化版本的无缝转换，
        确保现有代码可以逐步迁移到新的API设计。
        """
        generation_config = GenerationConfig(
            max_tokens=self.max_tokens,
            temperature=self.temperature or 0.7
        )

        stream_config = StreamConfig(enabled=self.stream)

        return OptimizedTextGenerationRequest(
            messages=self.messages,
            model=self.model,
            generation=generation_config,
            stream=stream_config,
            user=self.user,
            metadata=self.metadata
        )

    def validate_for_provider(self, provider_name: str) -> Dict[str, List[str]]:
        """验证简化请求是否与指定供应商兼容

        为简化版本提供同样的供应商兼容性检查功能。
        """
        # 先转换为优化版本，然后进行验证
        optimized = self.to_optimized()
        # 这里需要导入供应商验证功能，暂时返回空结果
        return {"errors": [], "warnings": [], "info": []}

    def get_provider_params(self, provider_name: str) -> Dict[str, Any]:
        """获取供应商特定参数

        为简化版本提供供应商适配功能。
        """
        optimized = self.to_optimized()
        # 这里需要导入供应商适配功能，暂时返回基础参数
        return optimized.to_legacy_format()

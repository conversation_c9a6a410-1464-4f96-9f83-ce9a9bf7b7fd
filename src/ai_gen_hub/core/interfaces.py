"""
AI Gen Hub 核心接口定义

定义了所有AI服务的抽象基类和标准接口，确保不同供应商的实现具有一致性。
这些接口遵循SOLID原则，提供了良好的扩展性和可维护性。

重构说明：
- 数据模型已移动到 models/ 子模块中
- 保持向后兼容性，通过重新导出提供相同的接口
- 适配器已移动到 adapters/ 子模块中
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, AsyncIterator, Dict, List, Optional, Union
from datetime import datetime
from enum import Enum

# 设置日志记录器
logger = logging.getLogger(__name__)

# 从新的模型模块导入所有数据模型（保持向后兼容性）
from .models.base import (
    ModelType,
    MessageRole,
    FinishReason,
    ProviderType,
    Message,
    Usage,
    BaseRequest,
    BaseResponse,
    BaseConfig,
)

from .models.requests import (
    TextGenerationRequest,
    ImageGenerationRequest,
    OptimizedTextGenerationRequest,
    SimpleTextGenerationRequest,
    GenerationConfig,
    StreamConfig,
    SafetyConfig,
)

from .models.responses import (
    TextGenerationResponse,
    TextGenerationChoice,
    TextGenerationStreamChunk,
    StreamChoice,
    ImageGenerationResponse,
    ImageData,
    OptimizedTextGenerationResponse,
    StreamChunk,
    StreamEvent,
    ErrorResponse,
    BatchTextGenerationResponse,
    Choice,
    UsageStats,
    PerformanceMetrics,
    ProviderInfo,
    ErrorInfo,
)

# 从适配器模块导入
from .adapters.request_adapter import RequestAdapter


# =============================================================================
# 额外的枚举定义（不在基础模型中的）
# =============================================================================

class ProviderStatus(str, Enum):
    """供应商状态枚举"""
    HEALTHY = "healthy"  # 健康状态
    DEGRADED = "degraded"  # 降级状态
    UNHEALTHY = "unhealthy"  # 不健康状态
    MAINTENANCE = "maintenance"  # 维护状态


class RequestStatus(str, Enum):
    """请求状态枚举"""
    PENDING = "pending"  # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


# =============================================================================
# 核心接口定义
# =============================================================================

class AIProvider(ABC):
    """AI供应商抽象基类
    
    定义了所有AI供应商必须实现的核心接口。每个具体的供应商适配器
    都应该继承这个基类并实现相应的方法。
    """
    
    def __init__(self, provider_name: str, config: Dict[str, Any]):
        """初始化供应商
        
        Args:
            provider_name: 供应商名称
            config: 供应商配置
        """
        self.provider_name = provider_name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{provider_name}")
    
    @abstractmethod
    async def generate_text(
        self, 
        request: Union[TextGenerationRequest, OptimizedTextGenerationRequest]
    ) -> Union[TextGenerationResponse, OptimizedTextGenerationResponse]:
        """生成文本
        
        Args:
            request: 文本生成请求
            
        Returns:
            TextGenerationResponse: 文本生成响应
        """
        pass
    
    @abstractmethod
    async def generate_text_stream(
        self, 
        request: Union[TextGenerationRequest, OptimizedTextGenerationRequest]
    ) -> AsyncIterator[Union[TextGenerationStreamChunk, StreamChunk]]:
        """流式生成文本
        
        Args:
            request: 文本生成请求
            
        Yields:
            TextGenerationStreamChunk: 流式文本生成响应块
        """
        pass
    
    @abstractmethod
    async def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResponse:
        """生成图像
        
        Args:
            request: 图像生成请求
            
        Returns:
            ImageGenerationResponse: 图像生成响应
        """
        pass
    
    @abstractmethod
    async def get_models(self) -> List[str]:
        """获取支持的模型列表
        
        Returns:
            List[str]: 支持的模型名称列表
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        pass


class ProviderManager(ABC):
    """供应商管理器抽象基类
    
    负责管理多个AI供应商，提供负载均衡、故障转移等功能。
    """
    
    @abstractmethod
    async def get_provider(self, model: str) -> AIProvider:
        """根据模型获取供应商
        
        Args:
            model: 模型名称
            
        Returns:
            AIProvider: 供应商实例
        """
        pass
    
    @abstractmethod
    async def register_provider(self, provider: AIProvider):
        """注册供应商
        
        Args:
            provider: 供应商实例
        """
        pass
    
    @abstractmethod
    async def get_provider_status(self) -> Dict[str, ProviderStatus]:
        """获取所有供应商状态
        
        Returns:
            Dict[str, ProviderStatus]: 供应商状态映射
        """
        pass


class CacheInterface(ABC):
    """缓存接口抽象基类"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存值"""
        pass
    
    @abstractmethod
    async def delete(self, key: str):
        """删除缓存值"""
        pass


class MonitoringInterface(ABC):
    """监控接口抽象基类"""
    
    @abstractmethod
    async def record_request(self, provider: str, model: str, request_time: float):
        """记录请求指标"""
        pass
    
    @abstractmethod
    async def record_error(self, provider: str, error_type: str, error_message: str):
        """记录错误指标"""
        pass
    
    @abstractmethod
    async def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标"""
        pass


# =============================================================================
# 导出所有公共接口和模型
# =============================================================================

__all__ = [
    # 基础枚举和模型
    "ModelType",
    "MessageRole", 
    "FinishReason",
    "ProviderType",
    "ProviderStatus",
    "RequestStatus",
    "Message",
    "Usage",
    "BaseRequest",
    "BaseResponse",
    "BaseConfig",
    
    # 请求模型
    "TextGenerationRequest",
    "ImageGenerationRequest",
    "OptimizedTextGenerationRequest",
    "SimpleTextGenerationRequest",
    "GenerationConfig",
    "StreamConfig",
    "SafetyConfig",
    
    # 响应模型
    "TextGenerationResponse",
    "TextGenerationChoice",
    "TextGenerationStreamChunk",
    "StreamChoice",
    "ImageGenerationResponse",
    "ImageData",
    "OptimizedTextGenerationResponse",
    "StreamChunk",
    "StreamEvent",
    "ErrorResponse",
    "BatchTextGenerationResponse",
    "Choice",
    "UsageStats",
    "PerformanceMetrics",
    "ProviderInfo",
    "ErrorInfo",
    
    # 适配器
    "RequestAdapter",
    
    # 接口
    "AIProvider",
    "ProviderManager",
    "CacheInterface",
    "MonitoringInterface",
]
